/**
 * API配置文件
 * 管理不同环境下的API端点配置
 */

// API端点配置接口
export interface ApiConfig {
  /** API基础URL */
  baseURL: string;
  /** 超时时间（毫秒） */
  timeout: number;
  /** 是否为生产环境 */
  isProduction: boolean;
}

// 环境检测
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

// 获取当前端口（用于本地开发时的端口判断）
const getCurrentPort = (): number => {
  if (typeof window !== 'undefined') {
    return parseInt(window.location.port) || 80;
  }
  return parseInt(process.env.PORT || '3000');
};

// 获取API配置
export const getApiConfig = (): ApiConfig => {
  const port = getCurrentPort();

  // 优先使用环境变量配置
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  const envTimeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000');

  // 生产环境构建时使用HTTPS端点
  if (isProduction) {
    return {
      baseURL: envApiUrl || 'https://advisor.sanva.tk/v2',
      timeout: envTimeout,
      isProduction: true,
    };
  }

  // 开发环境根据端口确定后端
  let baseURL = 'http://localhost:33001'; // 默认开发后端

  if (port === 54001) {
    baseURL = 'http://localhost:53011'; // 本地生产后端
  } else if (port === 34001) {
    baseURL = 'http://localhost:33001'; // 本地开发后端
  }

  return {
    baseURL: envApiUrl || baseURL,
    timeout: envTimeout,
    isProduction: false,
  };
};

// 默认API配置
export const defaultApiConfig = getApiConfig();

// API端点常量
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/admin/auth/login',
    LOGOUT: '/api/admin/auth/logout',
    ME: '/api/admin/auth/me',
    REFRESH: '/api/admin/auth/refresh',
  },
  
  // 用户管理
  USERS: {
    LIST: '/api/admin/users',
    DETAIL: (id: string) => `/api/admin/users/${id}`,
    CREATE: '/api/admin/users',
    UPDATE: (id: string) => `/api/admin/users/${id}`,
    DELETE: (id: string) => `/api/admin/users/${id}`,
  },
  
  // 聊天管理
  CHAT: {
    LIST: '/api/admin/chat',
    DETAIL: (id: string) => `/api/admin/chat/${id}`,
    MESSAGES: (id: string) => `/api/admin/chat/${id}/messages`,
  },
  
  // 财务管理
  FINANCIAL: {
    TRANSACTIONS: '/api/admin/financial/transactions',
    PRODUCTS: '/api/admin/financial/products',
    PRICING: '/api/admin/financial/pricing',
  },
  
  // 系统管理
  SYSTEM: {
    CONFIG: '/api/admin/system/config',
    STATUS: '/api/admin/system/status',
    VERSION: '/api/admin/system/check-version',
  },
  
  // 日志管理
  LOGS: {
    LIST: '/api/admin/logs',
    CLEAR: '/api/admin/logs/clear',
  },
  
  // 统计报表
  REPORTS: {
    DASHBOARD: '/api/admin/reports/dashboard',
    USERS: '/api/admin/reports/users',
    CHAT: '/api/admin/reports/chat',
    FINANCIAL: '/api/admin/reports/financial',
  },
} as const;

// 环境信息
export const ENV_INFO = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  VERSION: process.env.REACT_APP_VERSION || '1.0.0',
  IS_PRODUCTION: isProduction,
  IS_DEVELOPMENT: isDevelopment,
  CURRENT_PORT: getCurrentPort(),
  API_CONFIG: defaultApiConfig,
};

// 调试信息（仅在开发环境下输出）
if (isDevelopment && typeof console !== 'undefined') {
  console.log('🔧 API配置信息:', ENV_INFO);
}
